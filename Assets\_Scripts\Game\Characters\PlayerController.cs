using System;
using AnchorLight.Game.Characters;
using UnityEngine;

[RequireComponent(typeof(Rigidbody2D), typeof(Collider2D))]
public class PlayerController : MonoBehaviour, IPlayerController
{
    [SerializeField]
    public ScriptableStats _stats;
    private Rigidbody2D _rb;
    public Rigidbody2D RB => _rb;
    private CapsuleCollider2D _col;
    public CapsuleCollider2D Col => _col;
    private FrameInput _frameInput;
    private Vector2 _frameVelocity;
    private bool _cachedQueryStartInColliders;
    public Transform handAnchor;

    private bool _dashToConsume;
    private bool _isDashing;
    private float _dashTimeLeft;
    private Vector2 _dashDirection;
    private float _dashTraveled;
    public float _lastFacing = 1f;
    public PlayerAnimator playerAnimator;

    private bool _isCrouching;
    private bool _isSwimming;
    private bool _isDiving;
    private bool _isSubmerged;
    private float _origColHeight;
    private Vector2 _origColOffset;
    private Collider2D _waterCollider;

    // NEW: Gliding state
    private bool _isGliding;

    // Wall climbing state
    private bool _isWallClimbing;
    private float _wallGripTimeLeft;
    private int _wallDirection; // -1 for left wall, 1 for right wall

    // Rope climbing state
    private bool _isRopeClimbing;

    // IPlayerController implementation
    public bool IsSwimming => _isSwimming;
    public bool IsDiving => _isDiving;
    public bool IsWallClimbing => _isWallClimbing;
    public bool IsRopeClimbing
    {
        get => _isRopeClimbing;
        set => _isRopeClimbing = value;
    }

    public PlayerRopeState playerRopeState;

    #region Interface

    public Vector2 FrameInput => _frameInput.Move;
    public FrameInput FrameInputStruct => _frameInput;
    public bool JumpToConsume => _jumpToConsume;
    public Vector2 FrameVelocity
    {
        get => _frameVelocity;
        set => _frameVelocity = value;
    }
    public event Action<bool, float> GroundedChanged;
    public event Action Jumped;

    #endregion

    private float _time;

    private void Awake()
    {
        _rb = GetComponent<Rigidbody2D>();
        _col = GetComponent<CapsuleCollider2D>();
        playerRopeState = new PlayerRopeState(this, playerAnimator, _stats);
        // Cache original collider dimensions for crouch
        _origColHeight = _col.size.y;
        _origColOffset = _col.offset;

        _cachedQueryStartInColliders = Physics2D.queriesStartInColliders;
    }

    private void Update()
    {
        _time += Time.deltaTime;
        GatherInput();
    }

    private void GatherInput()
    {
        _frameInput = new FrameInput
        {
            JumpDown = Input.GetButtonDown("Jump"),
            JumpHeld = Input.GetButton("Jump"),
            Move = new Vector2(Input.GetAxisRaw("Horizontal"), Input.GetAxisRaw("Vertical")),
            DashDown = Input.GetKeyDown(KeyCode.C),
            CrouchHeld = Input.GetKey(KeyCode.X),
            // NEW: Added Glide input
            GlideHeld = Input.GetKey(KeyCode.G),
            InteractDown = Input.GetKeyDown(KeyCode.E),
        };

        if (_stats.SnapInput)
        {
            _frameInput.Move.x =
                Mathf.Abs(_frameInput.Move.x) < _stats.HorizontalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_frameInput.Move.x);
            _frameInput.Move.y =
                Mathf.Abs(_frameInput.Move.y) < _stats.VerticalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_frameInput.Move.y);
        }

        if (_frameInput.Move.x != 0)
            _lastFacing = _frameInput.Move.x;

        if (_frameInput.JumpDown)
        {
            _jumpToConsume = true;
            _timeJumpWasPressed = _time;
        }

        if (_frameInput.DashDown)
            _dashToConsume = true;
    }

    private void FixedUpdate()
    {
        CheckCollisions(); // This was missing!
        HandleCrouch();
        // If in water, swim and skip dash/jump
        if (_isSwimming)
        {
            HandleSubmerging();
            HandleSwimming();
            HandleRotation();
            ApplyMovement();
            return;
        }

        if (_isWallClimbing)
        {
            HandleWallClimbing();
            ApplyMovement();
            return;
        }

        playerRopeState.FixedUpdate();
        if (_isRopeClimbing)
        {
            ApplyMovement();
            // Rope state is handled in PlayerRopeState.FixedUpdate()
            // It manages its own physics, so we skip the standard movement logic.
            return;
        }

        HandleDash();

        if (!_isDashing)
        {
            HandleJump();
            HandleDirection();
            HandleGravity(); // Gliding logic is now inside HandleGravity
            HandleRotation();
        }

        ApplyMovement();
    }

    #region Collisions

    private float _frameLeftGrounded = float.MinValue;
    private bool _grounded;

    private void CheckCollisions()
    {
        Physics2D.queriesStartInColliders = false;

        // Ground and Ceiling
        bool groundHit = Physics2D.CapsuleCast(
            _col.bounds.center,
            _col.size,
            _col.direction,
            0,
            Vector2.down,
            _stats.GrounderDistance,
            ~_stats.PlayerLayer
        );
        bool ceilingHit = Physics2D.CapsuleCast(
            _col.bounds.center,
            _col.size,
            _col.direction,
            0,
            Vector2.up,
            _stats.GrounderDistance,
            ~_stats.PlayerLayer
        );

        // Wall detection
        CheckWallCollisions();

        // Hit a Ceiling
        if (ceilingHit)
            _frameVelocity.y = Mathf.Min(0, _frameVelocity.y);

        // Landed on the Ground
        if (!_grounded && groundHit)
        {
            _grounded = true;
            _coyoteUsable = true;
            _bufferedJumpUsable = true;
            _endedJumpEarly = false;
            _isGliding = false; // NEW: Stop gliding on ground
            _isWallClimbing = false; // Stop wall climbing on ground
            GroundedChanged?.Invoke(true, Mathf.Abs(_frameVelocity.y));
        }
        // Left the Ground
        else if (_grounded && !groundHit)
        {
            _grounded = false;
            _frameLeftGrounded = _time;
            GroundedChanged?.Invoke(false, 0);
        }

        Physics2D.queriesStartInColliders = _cachedQueryStartInColliders;
    }

    private void CheckWallCollisions()
    {
        // Don't attach to walls if grounded, swimming, rope climbing, or dashing
        if (_grounded || _isSwimming || _isRopeClimbing || _isDashing)
        {
            _isWallClimbing = false;
            return;
        }

        // Check for walls on both sides
        bool leftWall = Physics2D.Raycast(
            _col.bounds.center,
            Vector2.left,
            _stats.WallCheckDistance,
            _stats.WallLayer
        );
        bool rightWall = Physics2D.Raycast(
            _col.bounds.center,
            Vector2.right,
            _stats.WallCheckDistance,
            _stats.WallLayer
        );

        // Determine if we should attach to a wall
        bool shouldAttachToWall = false;
        int newWallDirection = 0;

        // Attach to left wall if moving left and hitting left wall
        if (leftWall && _frameInput.Move.x < 0)
        {
            shouldAttachToWall = true;
            newWallDirection = -1;
        }
        // Attach to right wall if moving right and hitting right wall
        else if (rightWall && _frameInput.Move.x > 0)
        {
            shouldAttachToWall = true;
            newWallDirection = 1;
        }

        // Start wall climbing if conditions are met
        if (shouldAttachToWall && !_isWallClimbing)
        {
            _isWallClimbing = true;
            _wallDirection = newWallDirection;
            _wallGripTimeLeft = _stats.WallGripDuration;
            _frameVelocity.x = 0; // Stop horizontal movement when attaching
        }
        // Stop wall climbing if no longer touching wall or changed direction
        else if (
            _isWallClimbing
            && (
                !leftWall && !rightWall
                || (_wallDirection == -1 && !leftWall)
                || (_wallDirection == 1 && !rightWall)
            )
        )
        {
            _isWallClimbing = false;
        }
    }

    #endregion


    #region Jumping

    private bool _jumpToConsume;
    private bool _bufferedJumpUsable;
    private bool _endedJumpEarly;
    private bool _coyoteUsable;
    private float _timeJumpWasPressed;

    private bool HasBufferedJump =>
        _bufferedJumpUsable && _time < _timeJumpWasPressed + _stats.JumpBuffer;
    private bool CanUseCoyote =>
        _coyoteUsable && !_grounded && _time < _frameLeftGrounded + _stats.CoyoteTime;

    private void HandleJump()
    {
        if (!_endedJumpEarly && !_grounded && !_frameInput.JumpHeld && _rb.linearVelocity.y > 0)
            _endedJumpEarly = true;

        if (!_jumpToConsume && !HasBufferedJump)
            return;

        if (_grounded || CanUseCoyote)
            ExecuteJump();
    }

    private void ExecuteJump()
    {
        // Preserve horizontal momentum while applying vertical jump power
        PerformJump(new Vector2(_frameVelocity.x, _stats.JumpPower));
    }

    #endregion

    #region Horizontal

    private void HandleDirection()
    {
        if (_isWallClimbing)
            return;

        if (_isGliding)
        {
            // While gliding, move forward at a set speed, ignoring horizontal input
            _frameVelocity.x = Mathf.MoveTowards(
                _frameVelocity.x,
                _lastFacing * _stats.GlideForwardSpeed,
                _stats.Acceleration * Time.fixedDeltaTime
            );
        }
        else
        {
            // Regular horizontal movement
            if (_frameInput.Move.x == 0)
            {
                var deceleration = _grounded ? _stats.GroundDeceleration : _stats.AirDeceleration;
                _frameVelocity.x = Mathf.MoveTowards(
                    _frameVelocity.x,
                    0,
                    deceleration * Time.fixedDeltaTime
                );
            }
            else
            {
                _frameVelocity.x = Mathf.MoveTowards(
                    _frameVelocity.x,
                    _frameInput.Move.x * _stats.MaxSpeed,
                    _stats.Acceleration * Time.fixedDeltaTime
                );
            }
        }
    }

    #endregion

    #region Rope
    public void ToggleRope(bool active)
    {
        _isRopeClimbing = active;
    }
    #endregion

    #region Gravity

    // MODIFIED: This method now includes the gliding logic.
    private void HandleGravity()
    {
        // Player is airborne, falling, and holding the glide button
        if (!_grounded && _frameInput.GlideHeld && _frameVelocity.y < 0)
        {
            _isGliding = true;
            // Clamp the fall speed to the glide speed
            _frameVelocity.y = Mathf.MoveTowards(
                _frameVelocity.y,
                -_stats.GlideFallSpeed,
                _stats.FallAcceleration * Time.fixedDeltaTime
            );
        }
        else
        {
            _isGliding = false;
            if (_grounded && _frameVelocity.y <= 0f)
            {
                _frameVelocity.y = _stats.GroundingForce;
            }
            else
            {
                var inAirGravity = _stats.FallAcceleration;
                if (_endedJumpEarly && _frameVelocity.y > 0)
                    inAirGravity *= _stats.JumpEndEarlyGravityModifier;
                _frameVelocity.y = Mathf.MoveTowards(
                    _frameVelocity.y,
                    -_stats.MaxFallSpeed,
                    inAirGravity * Time.fixedDeltaTime
                );
            }
        }
    }

    #endregion

    private void ApplyMovement() => _rb.linearVelocity = _frameVelocity;

    private void HandleRotation()
    {
        // Determine the target angle. Tilted if gliding, upright otherwise.
        // The tilt direction depends on the last direction the player was facing.
        float targetAngle = _isGliding ? _stats.GlideTiltAngle * -_lastFacing : 0f;

        //This is to prevent the player from rotating while climbing
        if (_isWallClimbing)
            targetAngle = 0;

        if (_isSwimming)
        {
            // Rotate player towards movement direction
            if (_frameInput.Move.magnitude > 0.1f)
            {
                // Subtract 90 degrees to align the player's 'up' with the movement direction
                targetAngle =
                    Mathf.Atan2(_frameInput.Move.y, _frameInput.Move.x) * Mathf.Rad2Deg - 90f;
            }
        }

        // Smoothly rotate the Rigidbody towards the target angle.
        // Using LerpAngle for a more natural-looking interpolation.
        // Use _rb.MoveRotation for physics-safe rotation in FixedUpdate.
        if (_isSwimming)
        {
            float smoothedAngle = Mathf.LerpAngle(
                _rb.rotation,
                targetAngle,
                _stats.SwimRotationSpeed * Time.fixedDeltaTime
            );
            _rb.MoveRotation(smoothedAngle);
        }
        else if (_isGliding)
        {
            float smoothedAngle = Mathf.LerpAngle(
                _rb.rotation,
                targetAngle,
                _stats.GlideRotationSpeed * Time.fixedDeltaTime
            );
            _rb.MoveRotation(smoothedAngle);
        }
        else
        {
            _rb.MoveRotation(targetAngle);
        }
    }

    private void HandleDash()
    {
        if (_dashToConsume && !_isDashing)
        {
            _isDashing = true;
            _dashTimeLeft = _stats.DashDuration;
            _dashTraveled = 0f;
            var dir =
                _frameInput.Move != Vector2.zero
                    ? _frameInput.Move.normalized
                    : new Vector2(_lastFacing, 0);
            _dashDirection = dir;
            _dashToConsume = false;
        }
        if (_isDashing)
        {
            _frameVelocity = _dashDirection * _stats.DashPower;
            _dashTimeLeft -= Time.fixedDeltaTime;
            _dashTraveled += _stats.DashPower * Time.fixedDeltaTime;
            if (_dashTimeLeft <= 0 || _dashTraveled >= _stats.DashDistance)
                _isDashing = false;
        }
    }

    /// <summary>
    /// Adjusts the capsule collider for crouch height reduction when holding X and restores on release
    /// </summary>
    private void HandleCrouch()
    {
        var stats = _stats;
        bool wantCrouch = _grounded && _frameInput.CrouchHeld;
        // Shrink collider when starting crouch
        if (wantCrouch && !_isCrouching)
        {
            _isCrouching = true;
            var newHeight = Mathf.Max(0.1f, _origColHeight - stats.CrouchHeightReduction);
            _col.size = new Vector2(_col.size.x, newHeight);
            _col.offset = _origColOffset - new Vector2(0, stats.CrouchHeightReduction / 2f);
        }
        // Restore collider when releasing crouch
        else if (!wantCrouch && _isCrouching)
        {
            _isCrouching = false;
            _col.size = new Vector2(_col.size.x, _origColHeight);
            _col.offset = _origColOffset;
        }
    }

    private void HandleSubmerging()
    {
        var stats = _stats;
        bool wantDive = _isSwimming && _frameInput.CrouchHeld;
        if (wantDive && !_isDiving)
        {
            _isDiving = true;
            var newHeight = Mathf.Max(0.1f, _origColHeight - stats.CrouchHeightReduction);
            _col.size = new Vector2(_col.size.x, newHeight);
            _col.offset = _origColOffset - new Vector2(0, stats.CrouchHeightReduction / 2f);
        }
        else if (!wantDive && _isDiving)
        {
            _isDiving = false;
            _col.size = new Vector2(_col.size.x, _origColHeight);
            _col.offset = _origColOffset;
        }
    }

    /// <summary>
    /// Swim movement: use input for 2D movement, disable dash/jump
    /// </summary>
    private void HandleSwimming()
    {
        if (_waterCollider == null)
            return;

        var move = _frameInput.Move;
        var waterSurfaceY = _waterCollider.bounds.max.y;
        var playerTopY = _col.bounds.max.y;

        // Prevent upward movement if at or above the surface
        if (playerTopY >= waterSurfaceY && move.y > 0)
        {
            move.y = 0;
        }

        // Calculate horizontal and vertical velocity based on input
        if (move.magnitude > 0.1f)
        {
            _frameVelocity = move.normalized * _stats.SwimSpeed;
        }
        else
        {
            // Apply drag when there's no input
            _frameVelocity = Vector2.MoveTowards(
                _frameVelocity,
                Vector2.zero,
                _stats.SwimDrag * Time.fixedDeltaTime
            );
        }

        // Apply buoyancy only when submerged and not actively moving vertically.
        if (playerTopY < waterSurfaceY && Mathf.Abs(move.y) < 0.1f)
        {
            _frameVelocity.y = Mathf.MoveTowards(
                _frameVelocity.y,
                _stats.Buoyancy,
                _stats.FallAcceleration * Time.fixedDeltaTime
            );
        }
    }

    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Water"))
        {
            _isSwimming = true;
            _isSubmerged = true;
            _waterCollider = other;
        }
        else if (other.CompareTag("BouncePad"))
        {
            // Only trigger when falling onto it
            if (_rb.linearVelocity.y <= -0.1f)
            {
                ExecuteBouncePad(other.GetComponent<BouncePad>());
            }
        }
    }

    private void ExecuteBouncePad(BouncePad pad)
    {
        if (pad == null || !pad.CanBounce)
            return;

        _frameVelocity.y = pad.BounceForce; // Apply the bounce to vertical velocity

        pad.TriggerEffects(); // Play Animation/Sound and start cooldown on pad side
    }

    private void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("Water"))
        {
            _isSwimming = false;
            _isSubmerged = false;
            _waterCollider = null;
        }
    }

    private void HandleWallClimbing()
    {
        // Handle wall jump
        if (_jumpToConsume)
        {
            ExecuteWallJump();
            return;
        }

        // Update grip timer
        _wallGripTimeLeft -= Time.fixedDeltaTime;

        // If grip time expired, start sliding down
        if (_wallGripTimeLeft <= 0)
        {
            _frameVelocity.x = 0;
            _frameVelocity.y = Mathf.MoveTowards(
                _frameVelocity.y,
                -_stats.WallSlideSpeed,
                _stats.FallAcceleration * Time.fixedDeltaTime
            );
        }
        else
        {
            // Still gripping - no movement
            _frameVelocity.x = 0;
            _frameVelocity.y = 0;
        }
    }

    public void ApplyVelocity(Vector2 vel)
    {
        _frameVelocity = vel;
    }

    public void PerformJump(Vector2 jumpVelocity)
    {
        _frameVelocity = jumpVelocity;
        _jumpToConsume = false;
        _endedJumpEarly = false;
        _timeJumpWasPressed = 0;
        _bufferedJumpUsable = false;
        _coyoteUsable = false;
        Jumped?.Invoke();
    }

    private void ExecuteWallJump()
    {
        _isWallClimbing = false;

        // Jump away from the wall at an angle based on current movement input
        Vector2 jumpDirection;

        if (_frameInput.Move.x != 0)
        {
            // Jump in the direction the player is pointing
            jumpDirection = new Vector2(
                _frameInput.Move.x * _stats.WallJumpForce.x,
                _stats.WallJumpForce.y
            );
        }
        else
        {
            // Default jump away from wall
            jumpDirection = new Vector2(
                -_wallDirection * _stats.WallJumpForce.x,
                _stats.WallJumpForce.y
            );
        }

        PerformJump(jumpDirection);
    }

#if UNITY_EDITOR
    private void OnValidate()
    {
        if (_stats == null)
            Debug.LogWarning(
                "Please assign a ScriptableStats asset to the Player Controller's Stats slot",
                this
            );
    }

    private void OnDrawGizmos()
    {
        if (_col == null || _stats == null)
            return;

        // Draw wall detection rays
        Gizmos.color = _isWallClimbing ? Color.green : Color.red;

        // Left wall detection
        Gizmos.DrawRay(_col.bounds.center, Vector2.left * _stats.WallCheckDistance);

        // Right wall detection
        Gizmos.DrawRay(_col.bounds.center, Vector2.right * _stats.WallCheckDistance);

        // Draw wall climbing state indicator
        if (_isWallClimbing)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(_col.bounds.center, 0.5f);
        }
    }
#endif
}

public struct FrameInput
{
    public bool JumpDown;
    public bool JumpHeld;
    public bool DashDown;
    public bool CrouchHeld;
    public bool InteractDown;

    // NEW: Added GlideHeld
    public bool GlideHeld;
    public Vector2 Move;
}

public interface IPlayerController
{
    public event Action<bool, float> GroundedChanged;

    public event Action Jumped;
    public Vector2 FrameInput { get; }
    public bool IsSwimming { get; }
    public bool IsDiving { get; }
    public bool IsWallClimbing { get; }
    public bool IsRopeClimbing { get; }
}
