using UnityEngine;

[RequireComponent(typeof(Rigidbody2D))]
public class Rope : MonoBeh<PERSON>our
{
    [Head<PERSON>("Behavior")]
    [Tooltip("Direction the player is allowed to face on the rope")]
    public RopeDirection Direction = RopeDirection.Both;

    [Header("Rope Settings")]
    [Tooltip("Prefab of a single rope segment with Rigidbody2D and HingeJoint2D")]
    public GameObject ropeSegmentPrefab;

    [Toolt<PERSON>("Anchor Rigidbody2D to connect the topmost segment's hinge")]
    public Rigidbody2D anchor;

    [Tooltip("Desired total rope length in units")]
    public float ropeLength = 5f;

    [Tooltip("Scale to apply uniformly to all rope segments")]
    public Vector3 segmentScale = Vector3.one;

    [Tooltip("Flip the x-axis of each subsequent segment")]
    public bool flipSubsequent = false;

    [Tooltip("Randomize the flip of each segment")]
    public bool randomizeFlip = false;

    [Header("Hinge Joint Limits")]
    public float hingeLowerLimit = -60f; // Adjusted for smoother swinging
    public float hingeUpperLimit = 60f;

    public GameObject[] Segments { get; private set; }

    private Rigidbody2D _connectedBody;
    private Joint2D _playerJoint; // Changed to base Joint2D to support different joint types
    private int _connectedSegmentIndex = -1;

    void Start()
    {
        if (ropeSegmentPrefab == null)
        {
            Debug.LogError("Rope segment prefab is not assigned.");
            return;
        }
        if (anchor == null)
        {
            Debug.LogError("Anchor Rigidbody2D is not assigned.");
            return;
        }

        if (transform.childCount == 0)
        {
            GenerateRope();
        }
        else
        {
            Segments = new GameObject[transform.childCount];
            for (int i = 0; i < transform.childCount; i++)
            {
                Segments[i] = transform.GetChild(i).gameObject;
            }
        }
    }

    public void GenerateRope()
    {
        if (Segments != null)
        {
            foreach (var seg in Segments)
            {
                if (seg != null)
                {
                    if (Application.isEditor && !Application.isPlaying)
                        DestroyImmediate(seg);
                    else
                        Destroy(seg);
                }
            }
        }

        Vector2 segmentLength = GetSegmentLength();
        if (segmentLength.y <= 0)
        {
            Debug.LogError(
                "Invalid segment length. Make sure prefab has a Renderer or Collider with size."
            );
            return;
        }

        int segmentCount = Mathf.CeilToInt(ropeLength / segmentLength.y);
        Segments = new GameObject[segmentCount];

        Rigidbody2D prevRigidbody = anchor;
        Vector3 startPos = transform.position;

        for (int i = 0; i < segmentCount; i++)
        {
            GameObject segment = Instantiate(ropeSegmentPrefab, transform);
            segment.name = "RopeSegment_" + i;

            Vector3 currentScale = segmentScale;
            if (flipSubsequent)
            {
                if (randomizeFlip)
                {
                    if (Random.value > 0.5f)
                    {
                        currentScale.x *= -1;
                    }
                }
                else
                {
                    if (i % 2 != 0)
                    {
                        currentScale.x *= -1;
                    }
                }
            }
            segment.transform.localScale = currentScale;

            // Fix positioning: Use full segment length
            segment.transform.position = startPos + Vector3.down * segmentLength.y * i;

            var segmentCollider = segment.GetComponent<Collider2D>();
            if (segmentCollider != null)
            {
                segmentCollider.isTrigger = true;
            }
            var prevSegmentCollider = prevRigidbody.GetComponent<Collider2D>();
            if (segmentCollider != null && prevSegmentCollider != null && prevRigidbody != anchor)
            {
                Physics2D.IgnoreCollision(segmentCollider, prevSegmentCollider);
            }

            Rigidbody2D rb = segment.GetComponent<Rigidbody2D>();
            if (rb == null)
            {
                Debug.LogError("Rope segment prefab is missing Rigidbody2D.");
                if (Application.isEditor && !Application.isPlaying)
                    DestroyImmediate(segment);
                else
                    Destroy(segment);
                return;
            }

            // Configure rigidbody for realistic rope physics
            rb.mass = 0.1f; // Light segments for realistic swinging
            rb.linearDamping = 0.1f; // Low damping for natural swing
            rb.angularDamping = 0.5f; // Some angular damping to prevent wild spinning
            rb.gravityScale = 1f; // Full gravity for natural hang

            HingeJoint2D hinge = segment.GetComponent<HingeJoint2D>();
            if (hinge == null)
            {
                hinge = segment.AddComponent<HingeJoint2D>();
            }

            hinge.connectedBody = prevRigidbody;
            hinge.autoConfigureConnectedAnchor = false;
            hinge.anchor = new Vector2(0, segmentLength.y * 0.5f); // Middle top of current segment
            hinge.connectedAnchor =
                (prevRigidbody == anchor) ? Vector2.zero : new Vector2(0, -segmentLength.y * 0.5f); // Middle bottom of previous

            // More realistic hinge limits for rope movement
            JointAngleLimits2D limits = new JointAngleLimits2D
            {
                min = -80f, // Wider range for more natural swinging
                max = 80f,
            };
            hinge.limits = limits;
            hinge.useLimits = true;
            hinge.enableCollision = false;

            // Remove motor - it dampens natural rope physics
            hinge.useMotor = false;

            prevRigidbody = rb;
            Segments[i] = segment;
        }
    }

    private Vector2 GetSegmentLength()
    {
        if (ropeSegmentPrefab == null)
        {
            Debug.LogWarning("ropeSegmentPrefab is not assigned.");
            return Vector2.zero;
        }

        var spriteRenderer = ropeSegmentPrefab.GetComponent<SpriteRenderer>();
        if (spriteRenderer != null && spriteRenderer.sprite != null)
        {
            Vector3 size = spriteRenderer.bounds.size;
            return new Vector2(size.x, size.y);
        }

        var collider = ropeSegmentPrefab.GetComponent<Collider2D>();
        if (collider != null)
        {
            Vector3 size = collider.bounds.size;
            return new Vector2(size.x, size.y);
        }

        Debug.LogWarning("No SpriteRenderer or Collider2D found on ropeSegmentPrefab.");
        return Vector2.zero;
    }

    public void Attach(Rigidbody2D playerBody, int segmentIndex)
    {
        if (
            _connectedBody != null
            || Segments == null
            || segmentIndex < 0
            || segmentIndex >= Segments.Length
        )
        {
            return;
        }

        _connectedBody = playerBody;
        _connectedSegmentIndex = segmentIndex;

        GameObject segment = Segments[_connectedSegmentIndex];

        // Remove any existing joints to avoid conflicts
        var existingJoints = playerBody.GetComponents<Joint2D>();
        foreach (var joint in existingJoints)
        {
            if (joint != null)
                Destroy(joint);
        }

        // Use DistanceJoint2D for realistic rope physics instead of HingeJoint2D
        DistanceJoint2D playerJoint = playerBody.gameObject.AddComponent<DistanceJoint2D>();
        _playerJoint = playerJoint;

        playerJoint.connectedBody = segment.GetComponent<Rigidbody2D>();
        playerJoint.autoConfigureDistance = false;

        // Calculate distance from player's hand anchor to segment center
        Vector3 handAnchorWorld = playerBody.GetComponent<PlayerController>().handAnchor.position;
        Vector3 segmentWorld = segment.transform.position;
        float ropeDistance = Vector3.Distance(handAnchorWorld, segmentWorld);

        // Ensure minimum rope distance to prevent too-tight constraints
        ropeDistance = Mathf.Max(ropeDistance, 0.5f);

        playerJoint.distance = ropeDistance;
        playerJoint.maxDistanceOnly = false; // FIXED: Keep exact distance for proper rope constraint
        playerJoint.enableCollision = false;

        // Set anchor points
        Vector3 handAnchorLocal = playerBody.transform.InverseTransformPoint(handAnchorWorld);
        playerJoint.anchor = handAnchorLocal;
        playerJoint.connectedAnchor = Vector2.zero; // Center of segment

        // Reduce player's drag to allow better swinging
        playerBody.linearDamping = 0.5f;
        playerBody.angularDamping = 0.5f;

        // Ensure player mass is reasonable for rope physics
        if (playerBody.mass > 5f)
        {
            playerBody.mass = 2f; // Reasonable mass for rope physics
        }
    }

    public void Detach()
    {
        if (_connectedBody != null && _playerJoint != null)
        {
            // Store current velocity before detaching for momentum preservation
            Vector2 velocity = _connectedBody.linearVelocity;

            // Restore player's original physics properties
            _connectedBody.linearDamping = 1f; // Restore normal drag
            _connectedBody.angularDamping = 1f;

            // Destroy the joint
            Destroy(_playerJoint);

            // Apply momentum in the direction of swing for realistic detachment
            Vector2 swingDirection = velocity.normalized;
            float swingMagnitude = velocity.magnitude;
            _connectedBody.linearVelocity = swingDirection * Mathf.Min(swingMagnitude * 1.2f, 15f);

            _connectedBody = null;
            _playerJoint = null;
            _connectedSegmentIndex = -1;
        }
    }

    public void Climb(float direction)
    {
        if (_connectedBody == null)
            return;

        int newSegmentIndex = _connectedSegmentIndex + (direction > 0 ? -1 : 1);
        if (newSegmentIndex >= 0 && newSegmentIndex < Segments.Length)
        {
            // Smoothly transition to new segment
            GameObject newSegment = Segments[newSegmentIndex];

            // Update the distance joint to connect to new segment
            if (_playerJoint is DistanceJoint2D distanceJoint)
            {
                distanceJoint.connectedBody = newSegment.GetComponent<Rigidbody2D>();

                // Recalculate distance to new segment
                Vector3 handAnchorWorld = _connectedBody
                    .GetComponent<PlayerController>()
                    .handAnchor.position;
                Vector3 segmentWorld = newSegment.transform.position;
                float newDistance = Vector3.Distance(handAnchorWorld, segmentWorld);
                newDistance = Mathf.Max(newDistance, 0.5f); // Ensure minimum distance
                distanceJoint.distance = newDistance;
            }

            _connectedSegmentIndex = newSegmentIndex;
        }
    }

    public void Swing(float swingInput)
    {
        if (
            _connectedBody == null
            || _connectedSegmentIndex < 0
            || _connectedSegmentIndex >= Segments.Length
        )
            return;

        // Get the rope segment the player is connected to
        var segmentRb = Segments[_connectedSegmentIndex].GetComponent<Rigidbody2D>();
        if (segmentRb == null)
            return;

        // Calculate pendulum force based on rope direction
        Vector3 ropeDirection = (
            segmentRb.transform.position - _connectedBody.transform.position
        ).normalized;
        Vector3 swingDirection = Vector3.Cross(ropeDirection, Vector3.forward).normalized;

        // Apply force perpendicular to the rope for realistic pendulum motion
        Vector2 swingForce = new Vector2(swingDirection.x, swingDirection.y) * swingInput * 15f;

        // Apply force to player for immediate response
        _connectedBody.AddForce(swingForce, ForceMode2D.Force);

        // Also apply counter-force to the rope segment for realistic rope physics
        segmentRb.AddForce(-swingForce * 0.3f, ForceMode2D.Force);
    }

    public int GetClosestSegmentIndex(Vector3 position)
    {
        if (Segments == null || Segments.Length == 0)
        {
            return -1;
        }

        int closestIndex = 0;
        float minDistance = Vector3.Distance(position, Segments[0].transform.position);
        for (int i = 1; i < Segments.Length; i++)
        {
            float distance = Vector3.Distance(position, Segments[i].transform.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                closestIndex = i;
            }
        }
        return closestIndex;
    }

#if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if (Segments == null)
            return;
        Gizmos.color = Color.cyan;
        for (int i = 0; i < Segments.Length; i++)
        {
            if (Segments[i] != null)
            {
                Gizmos.DrawWireSphere(Segments[i].transform.position, 0.2f);
                if (i > 0)
                {
                    Gizmos.DrawLine(
                        Segments[i - 1].transform.position,
                        Segments[i].transform.position
                    );
                }
            }
        }
    }
#endif
}
